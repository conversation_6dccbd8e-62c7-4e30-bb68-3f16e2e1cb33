<svg width="1200" height="800" viewBox="0 0 1200 800" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background with rounded corners -->
  <rect width="1200" height="800" rx="20" fill="white" />
  
  <!-- App Header -->
  <rect width="1200" height="80" rx="12" fill="url(#header-gradient)" />
  <rect x="50" y="25" width="200" height="30" rx="15" fill="white" opacity="0.9" />
  <rect x="1000" y="25" width="100" height="30" rx="15" fill="white" opacity="0.6" />
  
  <!-- Left Panel -->
  <rect x="50" y="120" width="300" height="640" rx="12" fill="#F9F9F9" />
  
  <!-- Steps in Left Panel -->
  <rect x="70" y="150" width="260" height="60" rx="8" fill="url(#step-gradient)" />
  <rect x="90" y="170" width="160" height="20" rx="4" fill="white" />
  
  <rect x="70" y="230" width="260" height="60" rx="8" fill="#FAFAFA" />
  <rect x="90" y="250" width="140" height="20" rx="4" fill="#E0E0E0" />
  
  <rect x="70" y="310" width="260" height="60" rx="8" fill="#FAFAFA" />
  <rect x="90" y="330" width="180" height="20" rx="4" fill="#E0E0E0" />
  
  <!-- Main Content Area -->
  <rect x="380" y="120" width="770" height="640" rx="12" fill="white" stroke="#F0F0F0" stroke-width="1" />
  
  <!-- Image Upload Area -->
  <rect x="410" y="150" width="710" height="220" rx="8" fill="#FAFAFA" stroke="#F0F0F0" stroke-dasharray="5 5" />
  <circle cx="765" cy="260" r="40" fill="url(#upload-gradient)" opacity="0.8" />
  <rect x="745" y="250" width="40" height="8" rx="4" fill="white" />
  <rect x="761" y="234" width="8" height="40" rx="4" fill="white" />
  
  <!-- Text Area -->
  <rect x="410" y="400" width="710" height="200" rx="8" fill="#FAFAFA" />
  <rect x="430" y="420" width="670" height="20" rx="4" fill="#F0F0F0" />
  <rect x="430" y="450" width="620" height="20" rx="4" fill="#F0F0F0" />
  <rect x="430" y="480" width="670" height="20" rx="4" fill="#F0F0F0" />
  <rect x="430" y="510" width="500" height="20" rx="4" fill="#F0F0F0" />
  <rect x="430" y="540" width="400" height="20" rx="4" fill="#F0F0F0" />
  
  <!-- Buttons -->
  <rect x="410" y="650" width="200" height="50" rx="25" fill="url(#button-gradient)" />
  <rect x="450" y="665" width="120" height="20" rx="4" fill="white" opacity="0.9" />
  
  <rect x="630" y="650" width="200" height="50" rx="25" fill="#FAFAFA" stroke="#E0E0E0" />
  <rect x="670" y="665" width="120" height="20" rx="4" fill="#AAAAAA" />
  
  <!-- Gradients -->
  <defs>
    <linearGradient id="header-gradient" x1="0" y1="0" x2="1200" y2="80" gradientUnits="userSpaceOnUse">
      <stop stop-color="#ff8a00" />
      <stop offset="1" stop-color="#ffc000" />
    </linearGradient>
    
    <linearGradient id="step-gradient" x1="70" y1="150" x2="330" y2="210" gradientUnits="userSpaceOnUse">
      <stop stop-color="#ff8a00" />
      <stop offset="1" stop-color="#ffa33a" />
    </linearGradient>
    
    <linearGradient id="upload-gradient" x1="725" y1="220" x2="805" y2="300" gradientUnits="userSpaceOnUse">
      <stop stop-color="#ff8a00" />
      <stop offset="1" stop-color="#ffc000" />
    </linearGradient>
    
    <linearGradient id="button-gradient" x1="410" y1="650" x2="610" y2="700" gradientUnits="userSpaceOnUse">
      <stop stop-color="#ff8a00" />
      <stop offset="1" stop-color="#ffc000" />
    </linearGradient>
  </defs>
</svg> 