# Server Configuration
PORT=3001
NODE_ENV=development
SERVER_URL=http://localhost:3001
CORS_ALLOWED_ORIGIN=http://localhost:5173

# LinkedIn OAuth Configuration - Primary App (Basic Posting)
LINKEDIN_CLIENT_ID=your_linkedin_client_id
LINKEDIN_CLIENT_SECRET=your_linkedin_client_secret

# LinkedIn Community Management API App (Advanced Analytics)
LINKEDIN_ANALYTICS_CLIENT_ID=your_linkedin_analytics_client_id
LINKEDIN_ANALYTICS_CLIENT_SECRET=your_linkedin_analytics_client_secret

# JWT Secret for token signing
JWT_SECRET=your_super_secret_jwt_key_here_make_it_long_and_random

# Supabase Configuration
SUPABASE_URL=https://ukorcrxjrtxrqoalffki.supabase.co
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVrb3JjcnhqcnR4cnFvYWxmZmtpIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODUzOTI3NywiZXhwIjoyMDY0MTE1Mjc3fQ.xggkAh2bIa5QNkxj3kBIMEJioU-4wOhM310vp-VvwHc

# Gemini AI Configuration - Multiple API Keys for Rate Limit Handling
# Primary API Key (highest priority)
GEMINI_API_KEY_1=your_primary_gemini_api_key_123

# Secondary API Key (fallback)
GEMINI_API_KEY_2=your_secondary_gemini_api_key_456

# Tertiary API Key (final fallback)
GEMINI_API_KEY_3=your_tertiary_gemini_api_key_789

# Legacy support (will be used as primary if GEMINI_API_KEY_1 is not set)
GEMINI_API_KEY=your_gemini_api_key
