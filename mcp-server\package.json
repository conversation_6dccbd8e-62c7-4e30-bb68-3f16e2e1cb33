{"name": "postwizz-mcp-server", "version": "1.0.0", "description": "MCP Server for PostWizz - LinkedIn Content Creator with Gemini AI", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "bun --watch src/index.ts", "setup-db": "node scripts/setup-database.js", "test-setup": "node scripts/test-setup.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.0.0", "@supabase/supabase-js": "^2.49.8", "axios": "^1.6.2", "cheerio": "^1.0.0-rc.12", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "https-proxy-agent": "^7.0.6", "jsonwebtoken": "^9.0.2", "linkedin-api-client": "^0.3.0", "node-cron": "^3.0.3", "puppeteer": "^24.10.2", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "user-agents": "^1.1.0", "zod": "^3.22.4"}, "devDependencies": {"@types/cheerio": "^0.22.35", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^24.0.3", "@types/node-cron": "^3.0.8", "@types/user-agents": "^1.0.4", "nodemon": "^3.0.1", "ts-node": "^10.9.1", "typescript": "^5.3.2"}}